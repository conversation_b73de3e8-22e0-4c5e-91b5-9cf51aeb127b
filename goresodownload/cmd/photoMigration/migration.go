package main

import (
	"context"
	"fmt"
	"strings"
	"time"

	gohelper "github.com/real-rm/gohelper"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gospeedmeter "github.com/real-rm/gospeedmeter"
	gostreaming "github.com/real-rm/gostreaming"
	"go.mongodb.org/mongo-driver/bson"
)

// migrateDisk performs migration for the specified disk using streaming
func migrateDisk(disk string, config Config) error {
	golog.Info("Starting migration", "disk", disk, "dryRun", config.DryRun)

	// Initialize speed meter for performance tracking
	speedMeter := gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})

	// Create context for streaming
	ctx := context.Background()

	// Get cursor for streaming
	cursor, err := getPropertiesCursor(config)
	if err != nil {
		return fmt.Errorf("failed to get properties cursor: %w", err)
	}

	// Process properties using streaming
	streamOpts := &gostreaming.StreamingOptions{
		Stream:        cursor,
		High:          10,   // Process up to 10 properties concurrently
		SpeedInterval: 5000, // Log speed every 5 seconds
		Verbose:       2,
		Process: func(item interface{}) error {
			return processPropertyItem(item, config, speedMeter)
		},
		End: func(err error) {
			// Check if error is not nil and has actual content
			if err != nil && err.Error() != "" {
				golog.Error("Stream ended with error", "error", err, "disk", disk, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			} else {
				golog.Info("Stream completed successfully", "disk", disk, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			}
		},
		Error: func(err error) {
			golog.Error("Streaming error", "error", err)
		},
	}

	// Start streaming processing
	if err := gostreaming.Streaming(ctx, streamOpts); err != nil {
		return fmt.Errorf("streaming failed: %w", err)
	}

	return nil
}

// processPropertyItem processes a single property item from the stream
func processPropertyItem(item interface{}, config Config, speedMeter *gospeedmeter.SpeedMeter) error {
	// Track processing speed
	speedMeter.Check("processed", 1)

	// Handle different possible types from MongoDB cursor
	var prop bson.M
	switch v := item.(type) {
	case bson.M:
		prop = v
	case bson.D:
		// Convert bson.D to bson.M
		prop = make(bson.M)
		for _, elem := range v {
			prop[elem.Key] = elem.Value
		}
	case map[string]interface{}:
		prop = bson.M(v)
	default:
		speedMeter.Check("errors", 1)
		golog.Error("Invalid property type", "actualType", fmt.Sprintf("%T", item), "value", item)
		return fmt.Errorf("invalid property type")
	}

	golog.Info("Processing property", "propID", prop["_id"])

	// Check if property needs processing
	if !needToProcess(prop, config.Disk) {
		golog.Info("Skipping property - already processed or no images", "propID", prop["_id"])
		speedMeter.Check("skipped", 1)
		return nil
	}

	// Process the property
	result, err := processProperty(prop, config)
	if err != nil {
		golog.Error("Failed to process property", "propID", prop["_id"], "error", err)
		speedMeter.Check("failed", 1)

		// Record failure in migration log
		migrationResult := MigrationResult{
			PropID:    fmt.Sprintf("%v", prop["_id"]),
			MT:        getTimeField(prop, "mt"),
			SrcFolder: getSrcFolderFromProp(prop),
			DstFolder: getDstFolderFromProp(prop),
			Status:    "processing_failed",
			ErrMsg:    err.Error(),
			Disk:      config.Disk,
		}

		if !config.DryRun {
			if logErr := insertImmigrationLog(migrationResult); logErr != nil {
				golog.Error("Failed to insert error log", "error", logErr)
			}
		}
		return nil // Don't stop streaming on individual property failure
	}

	if result.Success > 0 {
		speedMeter.Check("success", 1)

		// Record success in migration log
		migrationResult := MigrationResult{
			PropID:    fmt.Sprintf("%v", prop["_id"]),
			MT:        getTimeField(prop, "mt"),
			SrcFolder: getSrcFolder(result.ImagePaths),
			DstFolder: getDstFolder(result.PhoP),
			Status:    "success",
			ErrMsg:    "",
			Disk:      config.Disk,
		}

		if !config.DryRun {
			if logErr := insertImmigrationLog(migrationResult); logErr != nil {
				golog.Error("Failed to insert success log", "error", logErr)
			}
		}
	}

	golog.Info("Property processed",
		"propID", prop["_id"],
		"images", result.ImageCount,
		"success", result.Success,
		"failed", result.Failed,
		"srcFolder", getSrcFolder(result.ImagePaths),
		"dstFolder", getDstFolder(result.PhoP))

	return nil
}

// processProperty processes a single property and all its images
func processProperty(prop bson.M, config Config) (*PropertyResult, error) {
	propID := fmt.Sprintf("%v", prop["_id"])
	src, ok := prop["src"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid src field")
	}

	result := &PropertyResult{
		PropID:     propID,
		Processed:  true,
		ImageCount: 0,
		Success:    0,
		Failed:     0,
		Errors:     []error{},
		PhoP:       "",
		ImagePaths: []string{},
	}

	var imageInfos []*ImageInfo
	var phoHL []int32
	var tnHL int32

	// PHASE 1: Collect all image paths and resolve hash conflicts BEFORE processing files
	var imagePaths []string // [relativePath, ...]
	var err error

	switch src {
	case SRC_TRB, SRC_DDF:
		imagePaths, err = collectTRBDDFImagePaths(prop)
	case SRC_OTW, SRC_CLG:
		imagePaths, err = collectOTWCLGImagePaths(prop)
	default:
		return result, fmt.Errorf("unsupported src type: %s", src)
	}

	if err != nil {
		return result, fmt.Errorf("failed to collect image paths: %w", err)
	}

	result.ImageCount = len(imagePaths)

	// Resolve hash conflicts and pre-calculate all paths BEFORE processing any files
	hashSet := make(map[int32]bool)
	imageHashMap := make(map[string]int32)  // relativePath -> uniqueHash
	imagePathMap := make(map[string]string) // relativePath -> newPath

	// Pre-extract common fields once
	sid, ok := prop["sid"].(string)
	if !ok {
		return result, fmt.Errorf("invalid sid field in property")
	}

	// Pre-calculate common path components once
	var phoP string
	if config.Disk == "ca7" {
		// On ca7, check if we should skip phoP generation
		if shouldSkipPhoPGeneration(prop) {
			// Use existing phoP from the property
			if existingPhoP, ok := prop["phoP"].(string); ok {
				phoP = existingPhoP
				golog.Info("Using existing phoP on ca7", "propID", prop["_id"], "phoP", phoP)
			} else {
				golog.Error("shouldSkipPhoPGeneration returned true but no existing phoP found", "propID", prop["_id"])
				// Fallback to generating new phoP
				var err error
				phoP, err = generateNewPhoP(prop)
				if err != nil {
					return result, fmt.Errorf("failed to generate fallback phoP: %w", err)
				}
			}
		} else {
			// Generate new phoP
			var err error
			golog.Warn("there is no existing phoP, generating new one", "propID", prop["_id"])
			phoP, err = generateNewPhoP(prop)
			if err != nil {
				return result, fmt.Errorf("failed to generate phoP on ca7: %w", err)
			}
		}
	} else {
		// On ca6, always generate new phoP
		var err error
		phoP, err = generateNewPhoP(prop)
		if err != nil {
			return result, fmt.Errorf("failed to generate phoP on ca6: %w", err)
		}
	}

	for _, relativePath := range imagePaths {
		originalHash := levelStore.MurmurToInt32(relativePath)
		uniqueHash := ensureUniqueHash(originalHash, relativePath, hashSet)
		imageHashMap[relativePath] = uniqueHash
		hashSet[uniqueHash] = true
		phoHL = append(phoHL, uniqueHash)

		// Pre-calculate new path using the unique hash and pre-calculated phoP
		newPath, err := buildNewPathWithHash(phoP, sid, uniqueHash)
		if err != nil {
			return result, fmt.Errorf("failed to build new path for %s: %w", relativePath, err)
		}
		imagePathMap[relativePath] = newPath
	}

	// Generate thumbnail hash for the first image
	if len(imagePaths) > 0 {
		firstImagePath := imagePaths[0]
		thumbKey := firstImagePath + "-t"
		thumbHash := levelStore.MurmurToInt32(thumbKey)
		tnHL = ensureUniqueHash(thumbHash, thumbKey, hashSet)
		hashSet[tnHL] = true
	}

	// PHASE 2: Batch process all files with pre-calculated paths and hashes
	// imagePaths: [relativePath, ...]
	// imageHashMap: map[relativePath]uniqueHash
	// imagePathMap: map[relativePath]newPath
	batchResult, err := batchProcessImageFiles(imagePaths, imageHashMap, imagePathMap, config)
	if err != nil {
		return result, fmt.Errorf("batch processing failed: %w", err)
	}

	// Update result with batch processing results
	imageInfos = batchResult.ImageInfos
	result.Success = batchResult.Success
	result.Failed = batchResult.Failed
	result.Errors = append(result.Errors, batchResult.Errors...)

	// Generate thumbnail for the first image if any images were successfully processed
	if len(imageInfos) > 0 && !config.DryRun {
		firstImageInfo := imageInfos[0]
		// Generate thumbnail path using the predetermined tnHL hash
		thumbnailPath, err := buildNewPathWithHash(phoP, sid, tnHL)
		if err != nil {
			golog.Error("Failed to build thumbnail path with hash", "error", err)
		} else {
			// Use the new path as source since the file has been moved
			// Use the predetermined tnHL hash
			err := createThumbnail(firstImageInfo.NewPath, thumbnailPath, tnHL)
			if err != nil {
				golog.Error("Failed to create thumbnail", "error", err)
			}
		}
	}

	// Update database if any images were successfully processed
	if result.Success > 0 && !config.DryRun {
		// Use the pre-calculated phoP

		updateParams := DBUpdateParams{
			Prop:  prop,
			PhoHL: phoHL,
			TnHL:  tnHL,
			Disk:  config.Disk,
			PhoP:  phoP,
		}

		if err := updateDB(updateParams); err != nil {
			return result, fmt.Errorf("failed to update database: %w", err)
		}
	}

	// Set the generated phoP and collected image paths in result
	result.PhoP = phoP
	result.ImagePaths = imagePaths

	return result, nil
}

// getTimeField safely extracts time field from property
func getTimeField(prop bson.M, fieldName string) time.Time {
	if val, ok := prop[fieldName].(time.Time); ok {
		return val
	}
	return time.Time{}
}

// ensureUniqueHash ensures the hash is unique within the property by adding a suffix if needed
func ensureUniqueHash(originalHash int32, originalKey string, usedHashes map[int32]bool) int32 {
	// If hash is already unique, return it
	if !usedHashes[originalHash] {
		return originalHash
	}

	// Hash collision detected, try adding incremental suffixes
	counter := 1
	for {
		// Create new key with suffix
		newKey := fmt.Sprintf("%s_%d", originalKey, counter)
		newHash := levelStore.MurmurToInt32(newKey)

		// If this new hash is unique, use it
		if !usedHashes[newHash] {
			golog.Info("Hash collision resolved",
				"originalKey", originalKey,
				"originalHash", originalHash,
				"newKey", newKey,
				"newHash", newHash,
				"counter", counter)
			return newHash
		}

		counter++

		// Safety check to prevent infinite loop (very unlikely but good practice)
		if counter > 1000 {
			golog.Error("Too many hash collisions, using original hash",
				"originalKey", originalKey,
				"originalHash", originalHash,
				"counter", counter)
			return originalHash
		}
	}
}

// collectTRBDDFImagePaths collects all image relative paths for TRB/DDF without processing files
func collectTRBDDFImagePaths(prop bson.M) ([]string, error) {
	pho, ok := prop["pho"]
	if !ok {
		return nil, fmt.Errorf("pho field not found")
	}

	var phoCount int
	switch v := pho.(type) {
	case int:
		phoCount = v
	case int32:
		phoCount = int(v)
	case int64:
		phoCount = int(v)
	default:
		return nil, fmt.Errorf("invalid pho field type")
	}

	if phoCount <= 0 {
		return nil, fmt.Errorf("invalid pho count: %d", phoCount)
	}

	var imagePaths []string

	// Collect each image path (numbered from 1 to phoCount)
	for i := 1; i <= phoCount; i++ {
		params := PathBuildParams{
			Prop:     prop,
			Src:      prop["src"].(string),
			Sid:      prop["sid"].(string),
			ImageNum: i,
		}

		relativePath, err := buildOriginalPath(params)
		if err != nil {
			golog.Error("Failed to build original path", "propID", prop["_id"], "imageNum", i, "error", err)
			continue // Skip this image but continue with others
		}

		imagePaths = append(imagePaths, relativePath)
	}

	return imagePaths, nil
}

// collectOTWCLGImagePaths collects all image relative paths for OTW/CLG without processing files
func collectOTWCLGImagePaths(prop bson.M) ([]string, error) {
	phoIDs, ok := prop["phoIDs"]
	if !ok {
		return nil, fmt.Errorf("phoIDs field not found")
	}

	// Handle different types of phoIDs
	var imageIDs []interface{}
	switch v := phoIDs.(type) {
	case []interface{}:
		imageIDs = v
	case []string:
		for _, id := range v {
			imageIDs = append(imageIDs, id)
		}
	case []int:
		for _, id := range v {
			imageIDs = append(imageIDs, id)
		}
	default:
		return nil, fmt.Errorf("invalid phoIDs field type")
	}

	if len(imageIDs) == 0 {
		return nil, fmt.Errorf("empty phoIDs array")
	}

	var imagePaths []string

	// Collect each image path
	for i, imageID := range imageIDs {
		params := PathBuildParams{
			Prop:    prop,
			Src:     prop["src"].(string),
			Sid:     prop["sid"].(string),
			ImageID: imageID, // buildOriginalPath will handle type conversion
		}

		relativePath, err := buildOriginalPath(params)
		if err != nil {
			golog.Error("Failed to build original path", "propID", prop["_id"], "imageIndex", i, "imageID", imageID, "error", err)
			continue // Skip this image but continue with others
		}

		imagePaths = append(imagePaths, relativePath)
	}

	return imagePaths, nil
}

// shouldSkipPhoPGeneration determines if we should skip generating new phoP on ca7
// Returns true if the property already has a valid phoP that should be reused
func shouldSkipPhoPGeneration(prop bson.M) bool {
	// Check if property already has phoP field
	if phoP, ok := prop["phoP"].(string); ok && phoP != "" {
		golog.Info("Property already has phoP, skipping generation", "propID", prop["_id"], "phoP", phoP)
		return true
	}

	// If no phoP field or empty, we need to generate it
	golog.Info("Property missing phoP, will generate new one", "propID", prop["_id"])
	return false
}

// generateNewPhoP generates a new phoP path for the property
func generateNewPhoP(prop bson.M) (string, error) {
	propID := fmt.Sprintf("%v", prop["_id"])

	onD, ok := prop["onD"].(int)
	if !ok {
		return "", fmt.Errorf("missing or invalid OnD field in property %s", propID)
	}

	src, ok := prop["src"].(string)
	if !ok {
		return "", fmt.Errorf("missing or invalid src field in property %s", propID)
	}

	sid, ok := prop["sid"].(string)
	if !ok {
		return "", fmt.Errorf("missing or invalid sid field in property %s", propID)
	}

	ts := gohelper.DateToTime(onD)
	filePath, err := levelStore.GetFullFilePathForProp(ts, src, sid)
	if err != nil {
		return "", fmt.Errorf("failed to generate phoP for property %s: %w", propID, err)
	}

	golog.Info("Generated new phoP", "propID", propID, "phoP", filePath)
	return filePath, nil
}

// getSrcFolder returns the old relative folder path from imagePaths[0]
func getSrcFolder(imagePaths []string) string {
	if len(imagePaths) == 0 {
		return ""
	}

	// Extract directory part from first image path: "/L1/L2/filename" -> "/L1/L2"
	firstImagePath := imagePaths[0]
	if strings.Contains(firstImagePath, "/") {
		parts := strings.Split(firstImagePath, "/")
		if len(parts) >= 3 {
			// Return /L1/L2 format (old relative folder)
			return "/" + parts[1] + "/" + parts[2]
		}
	}

	return firstImagePath
}

// getDstFolder returns the new relative folder path from phoP
func getDstFolder(phoP string) string {
	if phoP == "" {
		return ""
	}

	// Extract directory part from phoP: "/L1/L2/filename" -> "/L1/L2"
	if strings.Contains(phoP, "/") {
		parts := strings.Split(phoP, "/")
		if len(parts) >= 3 {
			// Return /L1/L2 format (new relative folder)
			return "/" + parts[1] + "/" + parts[2]
		}
	}

	return phoP
}

// getSrcFolderFromProp returns the old relative folder path from property (fallback for error cases)
func getSrcFolderFromProp(prop bson.M) string {
	// Try to get existing phoP from property (old path)
	if existingPhoP, ok := prop["phoP"].(string); ok && existingPhoP != "" {
		// Extract directory part from existing phoP: "/L1/L2/filename" -> "/L1/L2"
		if strings.Contains(existingPhoP, "/") {
			parts := strings.Split(existingPhoP, "/")
			if len(parts) >= 3 {
				// Return /L1/L2 format (old relative folder)
				return "/" + parts[1] + "/" + parts[2]
			}
		}
	}

	return ""
}

// getDstFolderFromProp returns the new relative folder path from property (fallback for error cases)
func getDstFolderFromProp(prop bson.M) string {
	// Try to generate new phoP path
	propID := fmt.Sprintf("%v", prop["_id"])
	src, ok := prop["src"].(string)
	if !ok {
		golog.Error("Invalid src field for getDstFolderFromProp", "propID", propID)
		return ""
	}

	onD, ok := prop["onD"].(int)
	if !ok {
		golog.Error("Invalid onD field for getDstFolderFromProp", "propID", propID)
		return ""
	}

	sid, ok := prop["sid"].(string)
	if !ok {
		golog.Error("Invalid sid field for getDstFolderFromProp", "propID", propID)
		return ""
	}

	ts := gohelper.DateToTime(onD)
	newFilePath, err := levelStore.GetFullFilePathForProp(ts, src, sid)
	if err != nil {
		golog.Error("Failed to generate destination folder from prop", "propID", propID, "error", err)
		return ""
	}

	// Extract directory part from new path: "/L1/L2/filename" -> "/L1/L2"
	if strings.Contains(newFilePath, "/") {
		parts := strings.Split(newFilePath, "/")
		if len(parts) >= 3 {
			// Return /L1/L2 format (new relative folder)
			return "/" + parts[1] + "/" + parts[2]
		}
	}

	return newFilePath
}
